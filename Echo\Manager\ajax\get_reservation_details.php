<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo '<div class="alert alert-danger">Unauthorized access</div>';
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reservation_id'])) {
    $reservation_id = intval($_POST['reservation_id']);

    try {
        // Get detailed reservation information
        $query = "
            SELECT r.*,
                   CONCAT(c.FirstName, ' ', c.LastName) as CustomerName,
                   c.Email as CustomerEmail,
                   c.Phone as CustomerPhone,
                   tc.CompanyName as TravelCompanyName,
                   tc.ContactEmail as CompanyEmail,
                   tc.ContactPhone as CompanyPhone,
                   h.Name as HotelName,
                   h.Location as HotelLocation,
                   rt.TypeName as RoomType,
                   rt.DailyRate,
                   rt.Description as RoomDescription,
                   ci.RoomID,
                   rm.<PERSON><PERSON>,
                   DATEDIFF(r.EndDate, r.StartDate) as StayDuration,
                   (DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate) as TotalAmount
            FROM Reservations r
            LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
            LEFT JOIN TravelCompanies tc ON r.CompanyID = tc.CompanyID
            JOIN Hotels h ON r.HotelID = h.HotelID
            JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
            LEFT JOIN CheckIns ci ON r.ReservationID = ci.ReservationID
            LEFT JOIN Rooms rm ON ci.RoomID = rm.RoomID
            WHERE r.ReservationID = ?
        ";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $reservation_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($reservation = $result->fetch_assoc()) {
            // Format dates
            $start_date = date('F j, Y', strtotime($reservation['StartDate']));
            $end_date = date('F j, Y', strtotime($reservation['EndDate']));
            $created_date = date('F j, Y g:i A', strtotime($reservation['CreatedAt']));

            // Status badge color
            $status_class = '';
            switch ($reservation['Status']) {
                case 'Confirmed': $status_class = 'success'; break;
                case 'CheckedIn': $status_class = 'info'; break;
                case 'CheckedOut': $status_class = 'secondary'; break;
                case 'Canceled': $status_class = 'danger'; break;
                case 'NoShow': $status_class = 'dark'; break;
                case 'No Show Auto Cancel': $status_class = 'warning'; break;
                default: $status_class = 'warning';
            }

            echo '
            <div class="row">
                <div class="col-md-6">';

            if ($reservation['CustomerName']) {
                echo '<h5><i class="fa fa-user"></i> Customer Information</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Name:</strong></td><td>' . htmlspecialchars($reservation['CustomerName']) . '</td></tr>
                        <tr><td><strong>Email:</strong></td><td>' . htmlspecialchars($reservation['CustomerEmail'] ?: 'Not provided') . '</td></tr>
                        <tr><td><strong>Phone:</strong></td><td>' . htmlspecialchars($reservation['CustomerPhone'] ?: 'Not provided') . '</td></tr>
                    </table>';
            } else {
                echo '<h5><i class="fa fa-building"></i> Travel Company Booking</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Company:</strong></td><td>' . htmlspecialchars($reservation['TravelCompanyName'] ?: 'Unknown Company') . '</td></tr>
                        <tr><td><strong>Contact Email:</strong></td><td>' . htmlspecialchars($reservation['CompanyEmail'] ?: 'Not provided') . '</td></tr>
                        <tr><td><strong>Contact Phone:</strong></td><td>' . htmlspecialchars($reservation['CompanyPhone'] ?: 'Not provided') . '</td></tr>
                    </table>';
            }

            echo '</div>
                <div class="col-md-6">
                    <h5><i class="fa fa-building"></i> Hotel Information</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Hotel:</strong></td><td>' . htmlspecialchars($reservation['HotelName']) . '</td></tr>
                        <tr><td><strong>Location:</strong></td><td>' . htmlspecialchars($reservation['HotelLocation']) . '</td></tr>
                        <tr><td><strong>Room Type:</strong></td><td>' . htmlspecialchars($reservation['RoomType']) . '</td></tr>
                        <tr><td><strong>Room Number:</strong></td><td>' . htmlspecialchars($reservation['RoomNumber'] ?: 'Not assigned') . '</td></tr>
                    </table>
                </div>
            </div>

            <hr>

            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fa fa-calendar"></i> Reservation Details</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Reservation ID:</strong></td><td>#' . htmlspecialchars($reservation['ReservationID']) . '</td></tr>
                        <tr><td><strong>Check-in Date:</strong></td><td>' . $start_date . '</td></tr>
                        <tr><td><strong>Check-out Date:</strong></td><td>' . $end_date . '</td></tr>
                        <tr><td><strong>Duration:</strong></td><td>' . $reservation['StayDuration'] . ' night(s)</td></tr>
                        <tr><td><strong>Status:</strong></td><td><span class="badge badge-' . $status_class . '">' . htmlspecialchars($reservation['Status']) . '</span></td></tr>
                        <tr><td><strong>Created:</strong></td><td>' . $created_date . '</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5><i class="fa fa-money"></i> Pricing Information</h5>
                    <table class="table table-sm">
                        <tr><td><strong>Daily Rate:</strong></td><td>' . format_currency($reservation['DailyRate']) . '</td></tr>
                        <tr><td><strong>Total Amount:</strong></td><td><strong>' . format_currency($reservation['TotalAmount']) . '</strong></td></tr>
                        <tr><td><strong>Number of Guests:</strong></td><td>' . htmlspecialchars($reservation['NumberOfGuests'] ?: '1') . '</td></tr>
                        <tr><td><strong>Rate Type:</strong></td><td>' . htmlspecialchars($reservation['RateType'] ?: 'Daily') . '</td></tr>
                    </table>
                </div>
            </div>';

            if (!empty($reservation['RoomDescription'])) {
                echo '
                <hr>
                <h5><i class="fa fa-info-circle"></i> Room Description</h5>
                <p>' . htmlspecialchars($reservation['RoomDescription']) . '</p>';
            }

            if (!empty($reservation['SpecialRequests'])) {
                echo '
                <hr>
                <h5><i class="fa fa-comment"></i> Special Requests</h5>
                <p>' . htmlspecialchars($reservation['SpecialRequests']) . '</p>';
            }

        } else {
            echo '<div class="alert alert-warning">Reservation not found.</div>';
        }

    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error loading reservation details: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
} else {
    echo '<div class="alert alert-danger">Invalid request.</div>';
}

$conn->close();
?>
