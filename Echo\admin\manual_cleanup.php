<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is admin/manager
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['user_type'], ['admin', 'manager'])) {
    header('Location: ../auth/login.php');
    exit();
}

$message = '';
$message_type = '';

// Handle manual cleanup execution
if (isset($_POST['run_cleanup'])) {
    try {
        // Capture output from cleanup script
        ob_start();
        include('../cron/daily_cleanup.php');
        $cleanup_output = ob_get_clean();

        $message = "Cleanup script executed successfully!";
        $message_type = "success";

        // Log the manual execution
        $log_file = __DIR__ . '/../cron/cleanup_log.txt';
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] MANUAL EXECUTION by user {$_SESSION['user_name']} (ID: {$_SESSION['user_id']})\n";
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);

    } catch (Exception $e) {
        $message = "Error executing cleanup script: " . $e->getMessage();
        $message_type = "error";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Cleanup - Echo Hotels</title>

    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css?family=Lora:400,700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Cabin:400,500,600,700&display=swap" rel="stylesheet">

    <!-- Css Styles -->
    <link rel="stylesheet" href="../css/bootstrap.min.css" type="text/css">
    <link rel="stylesheet" href="../css/font-awesome.min.css" type="text/css">
    <link rel="stylesheet" href="../css/style.css" type="text/css">

    <style>
        .cleanup-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .cleanup-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .warning-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .danger-box {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .success-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .btn-cleanup {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cleanup:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateY(-2px);
        }

        .btn-cleanup:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .status-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #dfa974;
        }

        .status-number {
            font-size: 2rem;
            font-weight: bold;
            color: #dfa974;
        }

        .status-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>

<body>
    <!-- Header Section Begin -->
    <header class="header-section">
        <div class="top-nav">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <ul class="tn-left">
                            <li><i class="fa fa-phone"></i> (12) 345 67890</li>
                            <li><i class="fa fa-envelope"></i> <EMAIL></li>
                        </ul>
                    </div>
                    <div class="col-lg-6">
                        <div class="tn-right">
                            <a href="../profile/<?php echo $_SESSION['user_type']; ?>.php" class="bk-btn">Back to Dashboard</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="menu-item">
            <div class="container">
                <div class="row">
                    <div class="col-lg-2">
                        <div class="logo">
                            <a href="../index.php">
                                <img src="../img/logo.png" alt="">
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-10">
                        <div class="nav-menu">
                            <nav class="mainmenu">
                                <ul>
                                    <li><a href="../index.php">Home</a></li>
                                    <li><a href="auto_cancel_monitor.php">Monitor</a></li>
                                    <li><a href="../profile/<?php echo $_SESSION['user_type']; ?>.php">Dashboard</a></li>
                                    <li><a href="../auth/logout.php">Logout</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Header End -->

    <!-- Cleanup Section Begin -->
    <section class="cleanup-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title">
                        <span>System Administration</span>
                        <h2>Manual Cleanup Execution</h2>
                        <p>Execute the daily cleanup script manually when needed</p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8 offset-lg-2">
                    <div class="cleanup-card">
                        <?php if ($message): ?>
                            <div class="<?php echo $message_type === 'success' ? 'success-box' : 'danger-box'; ?>">
                                <h5><i class="fa fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                                <?php echo $message_type === 'success' ? 'Success' : 'Error'; ?></h5>
                                <p><?php echo htmlspecialchars($message); ?></p>
                            </div>
                        <?php endif; ?>

                        <!-- Current Status -->
                        <h4><i class="fa fa-dashboard"></i> Current System Status</h4>

                        <?php
                        // Get current statistics
                        date_default_timezone_set('Asia/Colombo');
                        $current_time = date('Y-m-d H:i:s');
                        $current_hour = (int)date('H');

                        // Count reservations without credit card
                        $without_cc_query = "
                            SELECT COUNT(*) as count
                            FROM Reservations r
                            WHERE r.Status = 'Confirmed'
                            AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '')
                            AND r.CustomerID IS NOT NULL
                            AND r.CompanyID IS NULL
                        ";
                        $without_cc_result = $conn->query($without_cc_query);
                        $without_cc_count = $without_cc_result->fetch_assoc()['count'];

                        // Count eligible for cancellation
                        $eligible_query = "
                            SELECT COUNT(*) as count
                            FROM Reservations r
                            WHERE r.Status = 'Confirmed'
                            AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '')
                            AND r.CustomerID IS NOT NULL
                            AND r.CompanyID IS NULL
                            AND (
                                (DATE(r.CreatedAt) = CURDATE() AND HOUR(NOW()) >= 19)
                                OR DATE(r.CreatedAt) < CURDATE()
                            )
                        ";
                        $eligible_result = $conn->query($eligible_query);
                        $eligible_count = $eligible_result->fetch_assoc()['count'];

                        // Count no-shows
                        $no_show_query = "
                            SELECT COUNT(*) as count
                            FROM Reservations r
                            WHERE r.Status = 'Confirmed'
                            AND r.StartDate < CURDATE()
                            AND NOT EXISTS (
                                SELECT 1 FROM CheckIns ci
                                WHERE ci.ReservationID = r.ReservationID
                            )
                        ";
                        $no_show_result = $conn->query($no_show_query);
                        $no_show_count = $no_show_result->fetch_assoc()['count'];
                        ?>

                        <div class="status-grid">
                            <div class="status-item">
                                <div class="status-number"><?php echo $without_cc_count; ?></div>
                                <div class="status-label">Without Credit Card</div>
                            </div>
                            <div class="status-item">
                                <div class="status-number"><?php echo $eligible_count; ?></div>
                                <div class="status-label">Eligible for Cancellation</div>
                            </div>
                            <div class="status-item">
                                <div class="status-number"><?php echo $no_show_count; ?></div>
                                <div class="status-label">Potential No-Shows</div>
                            </div>
                            <div class="status-item">
                                <div class="status-number"><?php echo $current_hour; ?>:00</div>
                                <div class="status-label">Current Hour</div>
                            </div>
                        </div>

                        <!-- Warning Messages -->
                        <?php if ($eligible_count > 0): ?>
                            <div class="warning-box">
                                <h5><i class="fa fa-exclamation-triangle"></i> Action Required</h5>
                                <p><strong><?php echo $eligible_count; ?> reservation(s)</strong> are eligible for automatic cancellation.</p>
                                <p>These reservations were made without credit card details and have passed the 7 PM deadline.</p>
                            </div>
                        <?php endif; ?>

                        <?php if ($no_show_count > 0): ?>
                            <div class="warning-box">
                                <h5><i class="fa fa-calendar-times-o"></i> No-Show Processing</h5>
                                <p><strong><?php echo $no_show_count; ?> reservation(s)</strong> may be no-shows requiring billing.</p>
                                <p>These reservations have passed their check-in date without check-in records.</p>
                            </div>
                        <?php endif; ?>

                        <!-- Manual Execution -->
                        <div class="danger-box">
                            <h5><i class="fa fa-warning"></i> Manual Cleanup Execution</h5>
                            <p><strong>Warning:</strong> This will immediately execute the daily cleanup script, which includes:</p>
                            <ul>
                                <li>Marking reservations without credit card details as "No Show Auto Cancel" (if past 7 PM deadline)</li>
                                <li>Creating billing records for no-show customers (LKR 1,500 fee)</li>
                                <li>Sending notification emails (if enabled)</li>
                                <li>Updating reservation statuses in the database</li>
                            </ul>
                            <p><strong>This action cannot be undone.</strong> Use only when necessary.</p>

                            <form method="POST" onsubmit="return confirm('Are you sure you want to execute the cleanup script? This action cannot be undone.');">
                                <button type="submit" name="run_cleanup" class="btn-cleanup">
                                    <i class="fa fa-play"></i> Execute Cleanup Script Now
                                </button>
                            </form>
                        </div>

                        <!-- Quick Links -->
                        <div style="text-align: center; margin-top: 30px;">
                            <a href="auto_cancel_monitor.php" class="btn btn-primary" style="margin: 5px;">
                                <i class="fa fa-dashboard"></i> View Monitor Dashboard
                            </a>
                            <a href="../cron/test_auto_cancel.php" class="btn btn-info" style="margin: 5px;">
                                <i class="fa fa-test"></i> Test Interface
                            </a>
                            <a href="../api/auto_cancel_status.php" class="btn btn-secondary" style="margin: 5px;" target="_blank">
                                <i class="fa fa-code"></i> API Status
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Cleanup Section End -->

    <!-- Js Plugins -->
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
</body>
</html>
