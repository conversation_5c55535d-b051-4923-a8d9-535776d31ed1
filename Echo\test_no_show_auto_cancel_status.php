<?php
/**
 * Test Script for "No Show Auto Cancel" Status
 * 
 * This script verifies that the new status is working correctly
 * in the database and UI components.
 */

require_once('config/db.php');

echo "<!DOCTYPE html>";
echo "<html><head><title>Test No Show Auto Cancel Status</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
    .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    .status-badge { padding: 5px 10px; border-radius: 15px; font-size: 12px; font-weight: bold; }
    .status-no-show-auto-cancel { background: #e2e3e5; color: #383d41; }
    .status-confirmed { background: #d4edda; color: #155724; }
    .status-canceled { background: #f8d7da; color: #721c24; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🧪 Test: No Show Auto Cancel Status</h1>";

try {
    // Test 1: Check if the new status exists in the database enum
    echo "<h3>Test 1: Database Schema Verification</h3>";
    
    $check_enum_query = "SHOW COLUMNS FROM Reservations LIKE 'Status'";
    $result = $conn->query($check_enum_query);
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $enum_values = $row['Type'];
        
        echo "<p><strong>Current Status Enum:</strong></p>";
        echo "<pre>" . htmlspecialchars($enum_values) . "</pre>";
        
        if (strpos($enum_values, 'No Show Auto Cancel') !== false) {
            echo "<p class='success'>✅ 'No Show Auto Cancel' status exists in database enum</p>";
        } else {
            echo "<p class='error'>❌ 'No Show Auto Cancel' status NOT found in database enum</p>";
            echo "<p>Please run the migration script: <a href='setup/add_no_show_auto_cancel_status.php'>add_no_show_auto_cancel_status.php</a></p>";
        }
    } else {
        echo "<p class='error'>❌ Could not retrieve Status enum information</p>";
    }
    
    // Test 2: Try to insert a test reservation with the new status
    echo "<h3>Test 2: Database Insert Test</h3>";
    
    // First, get a valid hotel and room type for testing
    $hotel_query = "SELECT HotelID FROM Hotels LIMIT 1";
    $hotel_result = $conn->query($hotel_query);
    
    $room_type_query = "SELECT RoomTypeID FROM RoomTypes LIMIT 1";
    $room_type_result = $conn->query($room_type_query);
    
    if ($hotel_result->num_rows > 0 && $room_type_result->num_rows > 0) {
        $hotel = $hotel_result->fetch_assoc();
        $room_type = $room_type_result->fetch_assoc();
        
        // Try to insert a test reservation with the new status
        $test_insert_query = "
            INSERT INTO Reservations (
                HotelID, RoomTypeID, StartDate, EndDate, Status, 
                CancellationReason, CreatedAt, UpdatedAt
            ) VALUES (?, ?, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 DAY), 
                     'No Show Auto Cancel', 'Test insertion for status verification', NOW(), NOW())
        ";
        
        $stmt = $conn->prepare($test_insert_query);
        $stmt->bind_param("ii", $hotel['HotelID'], $room_type['RoomTypeID']);
        
        if ($stmt->execute()) {
            $test_reservation_id = $conn->insert_id;
            echo "<p class='success'>✅ Successfully inserted test reservation with 'No Show Auto Cancel' status (ID: #$test_reservation_id)</p>";
            
            // Clean up the test record
            $cleanup_query = "DELETE FROM Reservations WHERE ReservationID = ?";
            $cleanup_stmt = $conn->prepare($cleanup_query);
            $cleanup_stmt->bind_param("i", $test_reservation_id);
            $cleanup_stmt->execute();
            echo "<p class='info'>ℹ️ Test reservation cleaned up</p>";
            
        } else {
            echo "<p class='error'>❌ Failed to insert test reservation: " . $conn->error . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ No hotels or room types found for testing</p>";
    }
    
    // Test 3: Show existing reservations with different statuses
    echo "<h3>Test 3: Current Reservations by Status</h3>";
    
    $status_query = "
        SELECT Status, COUNT(*) as Count
        FROM Reservations 
        GROUP BY Status 
        ORDER BY Count DESC
    ";
    $status_result = $conn->query($status_query);
    
    if ($status_result && $status_result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>Status</th><th>Count</th><th>Visual Preview</th></tr>";
        
        while ($status_row = $status_result->fetch_assoc()) {
            $status = $status_row['Status'];
            $count = $status_row['Count'];
            
            // Generate CSS class name for status badge
            $css_class = 'status-' . strtolower(str_replace(' ', '-', $status));
            
            echo "<tr>";
            echo "<td><strong>$status</strong></td>";
            echo "<td>$count</td>";
            echo "<td><span class='status-badge $css_class'>$status</span></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='info'>ℹ️ No reservations found in database</p>";
    }
    
    // Test 4: Show recent auto-cancelled reservations
    echo "<h3>Test 4: Recent Auto-Cancelled Reservations</h3>";
    
    $auto_cancel_query = "
        SELECT r.ReservationID, r.Status, r.CancellationReason, r.UpdatedAt,
               CONCAT(COALESCE(c.FirstName, 'N/A'), ' ', COALESCE(c.LastName, '')) as CustomerName,
               h.Name as HotelName
        FROM Reservations r
        LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
        LEFT JOIN Hotels h ON r.HotelID = h.HotelID
        WHERE r.Status = 'No Show Auto Cancel'
        ORDER BY r.UpdatedAt DESC
        LIMIT 10
    ";
    
    $auto_cancel_result = $conn->query($auto_cancel_query);
    
    if ($auto_cancel_result && $auto_cancel_result->num_rows > 0) {
        echo "<p class='info'>Found " . $auto_cancel_result->num_rows . " auto-cancelled reservations:</p>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Customer</th><th>Hotel</th><th>Status</th><th>Reason</th><th>Updated</th></tr>";
        
        while ($reservation = $auto_cancel_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>#{$reservation['ReservationID']}</td>";
            echo "<td>" . htmlspecialchars($reservation['CustomerName']) . "</td>";
            echo "<td>" . htmlspecialchars($reservation['HotelName']) . "</td>";
            echo "<td><span class='status-badge status-no-show-auto-cancel'>{$reservation['Status']}</span></td>";
            echo "<td>" . htmlspecialchars(substr($reservation['CancellationReason'], 0, 50)) . "...</td>";
            echo "<td>" . date('M j, Y H:i', strtotime($reservation['UpdatedAt'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='success'>✅ No auto-cancelled reservations found (this is normal if the system hasn't run yet)</p>";
    }
    
    // Test 5: System Integration Test
    echo "<h3>Test 5: System Integration Status</h3>";
    
    $integration_tests = [
        'Database Schema' => strpos($enum_values ?? '', 'No Show Auto Cancel') !== false,
        'Cleanup Script' => file_exists(__DIR__ . '/cron/daily_cleanup.php'),
        'Test Interface' => file_exists(__DIR__ . '/cron/test_auto_cancel.php'),
        'Monitor Dashboard' => file_exists(__DIR__ . '/admin/auto_cancel_monitor.php'),
        'API Endpoint' => file_exists(__DIR__ . '/api/auto_cancel_status.php'),
    ];
    
    echo "<table>";
    echo "<tr><th>Component</th><th>Status</th></tr>";
    
    foreach ($integration_tests as $component => $status) {
        $status_text = $status ? '✅ Working' : '❌ Missing';
        $status_class = $status ? 'success' : 'error';
        
        echo "<tr>";
        echo "<td>$component</td>";
        echo "<td class='$status_class'>$status_text</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Summary
    echo "<h3>📋 Summary</h3>";
    $all_tests_passed = true;
    foreach ($integration_tests as $status) {
        if (!$status) {
            $all_tests_passed = false;
            break;
        }
    }
    
    if ($all_tests_passed && strpos($enum_values ?? '', 'No Show Auto Cancel') !== false) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745;'>";
        echo "<h4 class='success'>🎉 All Tests Passed!</h4>";
        echo "<p>The 'No Show Auto Cancel' status is properly configured and ready to use.</p>";
        echo "<ul>";
        echo "<li>✅ Database schema updated</li>";
        echo "<li>✅ Auto-cancellation script updated</li>";
        echo "<li>✅ UI components support new status</li>";
        echo "<li>✅ Monitoring tools available</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; border-left: 4px solid #dc3545;'>";
        echo "<h4 class='error'>⚠️ Some Tests Failed</h4>";
        echo "<p>Please review the test results above and fix any issues before using the system.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Test failed with error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<div style='margin-top: 30px; text-align: center;'>";
echo "<a href='cron/test_auto_cancel.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Auto-Cancel System</a>";
echo "<a href='admin/auto_cancel_monitor.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Monitor Dashboard</a>";
echo "<a href='setup/add_no_show_auto_cancel_status.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Run Migration</a>";
echo "</div>";

echo "</div></body></html>";

$conn->close();
?>
