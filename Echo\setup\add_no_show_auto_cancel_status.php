<?php
/**
 * Database Migration Script
 * Add "No Show Auto Cancel" status to Reservations table
 */

require_once('../config/db.php');

echo "<!DOCTYPE html>";
echo "<html><head><title>Database Migration - Add No Show Auto Cancel Status</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
    .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔄 Database Migration: Add No Show Auto Cancel Status</h1>";

try {
    // Check current status enum
    echo "<h3>📋 Current Status Enum Values</h3>";
    $check_query = "SHOW COLUMNS FROM Reservations LIKE 'Status'";
    $result = $conn->query($check_query);
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "<p><strong>Current Status Enum:</strong></p>";
        echo "<pre>" . htmlspecialchars($row['Type']) . "</pre>";
        
        // Check if "No Show Auto Cancel" already exists
        if (strpos($row['Type'], 'No Show Auto Cancel') !== false) {
            echo "<p class='warning'>⚠️ 'No Show Auto Cancel' status already exists in the enum.</p>";
        } else {
            echo "<p class='info'>ℹ️ Adding 'No Show Auto Cancel' to the status enum...</p>";
            
            // Update the enum to include the new status
            $update_query = "ALTER TABLE Reservations MODIFY COLUMN Status enum(
                'Confirmed',
                'Canceled', 
                'NoShow',
                'No-Show',
                'No Show Auto Cancel',
                'CheckedIn',
                'CheckedOut'
            ) DEFAULT 'Confirmed'";
            
            if ($conn->query($update_query) === TRUE) {
                echo "<p class='success'>✅ Successfully added 'No Show Auto Cancel' status to Reservations table!</p>";
                
                // Verify the change
                $verify_result = $conn->query($check_query);
                if ($verify_result && $verify_result->num_rows > 0) {
                    $verify_row = $verify_result->fetch_assoc();
                    echo "<p><strong>Updated Status Enum:</strong></p>";
                    echo "<pre>" . htmlspecialchars($verify_row['Type']) . "</pre>";
                }
            } else {
                throw new Exception("Failed to update Status enum: " . $conn->error);
            }
        }
    } else {
        throw new Exception("Could not retrieve current Status enum information");
    }
    
    // Show current reservations that would be affected
    echo "<h3>📊 Current Reservations Without Credit Cards</h3>";
    
    $reservations_query = "
        SELECT r.ReservationID, r.Status, r.CreatedAt,
               CONCAT(c.FirstName, ' ', c.LastName) as CustomerName,
               h.Name as HotelName, rt.TypeName as RoomType
        FROM Reservations r
        LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
        LEFT JOIN Hotels h ON r.HotelID = h.HotelID
        LEFT JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        WHERE r.Status = 'Confirmed'
        AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '')
        AND r.CustomerID IS NOT NULL
        AND r.CompanyID IS NULL
        ORDER BY r.CreatedAt DESC
        LIMIT 10
    ";
    
    $reservations_result = $conn->query($reservations_query);
    
    if ($reservations_result && $reservations_result->num_rows > 0) {
        echo "<p>Found <strong>" . $reservations_result->num_rows . "</strong> reservations without credit cards (showing latest 10):</p>";
        echo "<table border='1' cellpadding='8' cellspacing='0' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Customer</th><th>Hotel</th><th>Room Type</th><th>Status</th><th>Created</th>";
        echo "</tr>";
        
        while ($reservation = $reservations_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>#{$reservation['ReservationID']}</td>";
            echo "<td>" . htmlspecialchars($reservation['CustomerName']) . "</td>";
            echo "<td>" . htmlspecialchars($reservation['HotelName']) . "</td>";
            echo "<td>" . htmlspecialchars($reservation['RoomType']) . "</td>";
            echo "<td><span style='background: #007bff; color: white; padding: 2px 6px; border-radius: 3px;'>{$reservation['Status']}</span></td>";
            echo "<td>" . date('M j, Y H:i', strtotime($reservation['CreatedAt'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p class='info'>ℹ️ These reservations will be marked as 'No Show Auto Cancel' when the daily cleanup script runs at 7 PM.</p>";
    } else {
        echo "<p class='success'>✅ No reservations without credit cards found.</p>";
    }
    
    // Test the new status
    echo "<h3>🧪 Testing New Status</h3>";
    echo "<p>Testing if we can insert a reservation with the new status...</p>";
    
    $test_query = "SELECT 'No Show Auto Cancel' as test_status";
    $test_result = $conn->query($test_query);
    
    if ($test_result) {
        echo "<p class='success'>✅ New status value is working correctly!</p>";
    } else {
        echo "<p class='error'>❌ Error testing new status: " . $conn->error . "</p>";
    }
    
    echo "<h3>📝 Next Steps</h3>";
    echo "<ul>";
    echo "<li>✅ Database schema updated successfully</li>";
    echo "<li>🔄 Auto-cancellation script will now use 'No Show Auto Cancel' status</li>";
    echo "<li>🎨 UI components will display the new status with appropriate styling</li>";
    echo "<li>📊 Reports and dashboards will include the new status category</li>";
    echo "</ul>";
    
    echo "<h3>🔧 Auto-Cancellation Policy</h3>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
    echo "<p><strong>Policy:</strong> Reservations made without credit card details will be automatically cancelled at 7:00 PM daily.</p>";
    echo "<p><strong>New Status:</strong> These cancelled reservations will be marked as 'No Show Auto Cancel' instead of 'Cancelled'.</p>";
    echo "<p><strong>Distinction:</strong> This helps differentiate between manual cancellations and automatic policy-based cancellations.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Migration failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}

echo "<div style='margin-top: 30px; text-align: center;'>";
echo "<a href='../cron/test_auto_cancel.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Auto-Cancel System</a>";
echo "<a href='../admin/auto_cancel_monitor.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>View Monitor Dashboard</a>";
echo "<a href='../index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Back to Home</a>";
echo "</div>";

echo "</div></body></html>";

$conn->close();
?>
